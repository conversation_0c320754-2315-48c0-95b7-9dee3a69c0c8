{"configurations": [{"name": "STM32F4", "includePath": ["${workspaceFolder}/Libraries/CMSIS/Include", "${workspaceFolder}/Libraries/STM32F4xx_StdPeriph_Driver/inc", "${workspaceFolder}/User", "${workspaceFolder}/HardWare", "${workspaceFolder}/Start", "${workspaceFolder}/Library", "${workspaceFolder}/**"], "defines": ["STM32F40_41xxx", "USE_STDPERIPH_DRIVER"], "compilerPath": "", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "gcc-arm"}], "version": 4}