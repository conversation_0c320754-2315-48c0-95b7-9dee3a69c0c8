/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V2.0 - 优化版
  * @date    2024
  * @brief   STM32F407电路模型探究装置主程序 - G题专用
  *          基于嘉立创"天空星"STM32F407VGT6开发板
  ******************************************************************************
  * @attention
  *
  * 硬件配置：
  * - I2C OLED: PB6(SCL)/PB7(SDA), 地址0x78
  * - 14位并行ADC: PC0-PC13数据线, PA0(EXTI0)时钟
  * - 串口调试: PA9(TX)/PA10(RX), 115200波特率
  * - 按键: PE4(KEY0)/PE3(KEY1)
  * - DDS输出: PA4(DAC1)
  *
  * 优化特性：
  * - 使用CMSIS-DSP高精度数学库
  * - ADC采样率提升至500kSPS
  * - DDS硬件定时器精确触发
  * - 调试输出可编译时关闭
  * - 高精度互相关相位测量算法
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"

// 数学常数定义 - 头文件已在main.h中包含
// PI宏已在arm_math.h中定义，避免重复定义
#ifndef PI
#define PI 3.14159265358979323846f
#endif
#define TWO_PI (2.0f * PI)

// 使用CMSIS-DSP库和标准库的高精度数学函数
// sqrtf, log10f, logf, powf, cosf, sinf 现在使用标准库实现

/* Private includes ----------------------------------------------------------*/
#include "adc_dma.h"
#include "parallel_adc.h"
#include "dds_wavegen.h"
#include "oled.h"
#include "key.h"
#include "usart.h"
#include "systick.h"
#include "delay.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

// 全局状态变量
volatile bool g_system_initialized = false;
volatile bool g_data_ready_flag = false;
volatile bool g_parallel_adc_data_ready = false;
volatile bool g_dds_update_complete = false;

// 配置结构体
ADC_Config_t g_adc_config;
ParallelADC_Config_t g_parallel_config;
DDS_Config_t g_dds_config;
AGC_Config_t g_agc_config;

// 调试缓冲区
char debug_buffer[256];

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void System_Init(void);
void System_StateMachine(void);
void AGC_Update(float signal_amplitude);
void G_DDS_UpdateOutput(void);
float DSP_CalculatePhase(float* signal1, float* signal2, uint16_t length);
void DSP_SimpleFFT(float* input, float* output, uint16_t length);
void FreqSweep_SinglePoint(uint32_t frequency);
void DataAcquisition_GetData(void);
void System_UpdateDisplay(void);

/**
  * @brief  Main program - 优化版
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    System_Init();

    /* 显示启动信息 */
    OLED_Clear();
    OLED_ShowString(0, 0, "Circuit Explorer", 8, 1);
    OLED_ShowString(0, 16, "STM32F407 G-Type", 8, 1);
    OLED_ShowString(0, 32, "Initializing...", 8, 1);
    OLED_Refresh();
    Delay_ms(2000);

    /* 系统自检 */
    OLED_ShowString(0, 48, "Self Check...", 8, 1);
    OLED_Refresh();
    Delay_ms(1000);

    /* 显示就绪状态 */
    OLED_Clear();
    OLED_ShowString(0, 0, "System Ready", 8, 1);
    OLED_ShowString(0, 16, "KEY0: Freq Sweep", 8, 1);
    OLED_ShowString(0, 32, "KEY1: Single Test", 8, 1);
    OLED_ShowString(0, 48, "Waiting...", 8, 1);
    OLED_Refresh();

    g_system_initialized = true;

    /* 主循环 - 优化版 */
    while (1)
    {
        /* 按键扫描和处理 */
        Key_Number_t key = Key_Scan();
        if (key != KEY_NONE) {
            switch(key) {
                case KEY_0:
                    // 启动频率扫描
                    OLED_ShowString(0, 48, "Freq Sweep...", 8, 1);
                    OLED_Refresh();
                    // 这里调用频率扫描函数
                    break;

                case KEY_1:
                    // 单点测量
                    OLED_ShowString(0, 48, "Single Test...", 8, 1);
                    OLED_Refresh();
                    // 这里调用单点测量函数
                    break;

                default:
                    break;
            }
        }

        /* 数据采集处理 */
        if (g_data_ready_flag) {
            g_data_ready_flag = false;

            // 获取ADC数据
            uint16_t adc_data[8];
            if (ADC_GetData(adc_data, 8) == 0) {
                // 处理ADC数据
                DataAcquisition_GetData();

#if DEBUG_ENABLE
                // 通过串口输出统计信息 (仅在调试模式下)
                ADC_Stats_t adc_stats;
                ADC_GetStats(&adc_stats);

                sprintf(debug_buffer, "ADC: Samples=%lu, Rate=%.1fkSPS\r\n",
                        adc_stats.total_samples, adc_stats.actual_sample_rate/1000.0f);
                USART_SendString(debug_buffer);
#endif
            }
        }

        /* 系统状态机处理 */
        System_StateMachine();

        /* DDS输出现在由TIM6中断自动触发，无需在主循环中调用 */

        /* 自动增益控制更新 */
        static uint32_t agc_counter = 0;
        if (++agc_counter >= 50000) { // 控制AGC更新频率
            agc_counter = 0;

            uint16_t adc_data[8];
            if (ADC_GetData(adc_data, 8) == 0) {
                float signal_amplitude = (float)adc_data[0] * 5.0f / 32768.0f; // 转换为电压
                AGC_Update(signal_amplitude);

#if DEBUG_ENABLE
                // 显示实时数据 (仅在调试模式下)
                sprintf(debug_buffer, "Sig: %.3fV, Gain: %d, Freq: %luHz\r\n",
                        signal_amplitude, g_agc_config.current_gain, g_dds_config.frequency);
                USART_SendString(debug_buffer);
#endif
            }
        }

        /* 检查并行ADC数据 */
        if (g_parallel_adc_data_ready) {
            g_parallel_adc_data_ready = false;
            uint16_t data;
            if (ParallelADC_ReadSingle(&data) == 0) {
#if DEBUG_ENABLE
                sprintf(debug_buffer, "Parallel ADC: %d\r\n", data);
                USART_SendString(debug_buffer);
#endif
            }
        }

        /* 检查DDS更新 */
        if (g_dds_update_complete) {
            g_dds_update_complete = false;

#if DEBUG_ENABLE
            DDS_Stats_t dds_stats;
            DDS_GetStats(&dds_stats);

            sprintf(debug_buffer, "DDS: Freq=%.1fHz, Samples=%lu\r\n",
                    dds_stats.actual_frequency, dds_stats.output_samples);
            USART_SendString(debug_buffer);
#endif
        }

        /* 系统显示更新 */
        static uint32_t display_counter = 0;
        if (++display_counter >= 500000) { // 控制显示更新频率
            display_counter = 0;
            System_UpdateDisplay();
        }

        /* 系统状态监控 */
        static uint32_t last_status_time = 0;
        if (SysTick_GetTick() - last_status_time > 5000) { // 每5秒输出一次状态
            last_status_time = SysTick_GetTick();

#if DEBUG_ENABLE
            sprintf(debug_buffer, "System Uptime: %lu ms\r\n", SysTick_GetUptime_ms());
            USART_SendString(debug_buffer);
#endif
        }
    }
}

/**
  * @brief  系统初始化函数 - 优化版
  * @param  None
  * @retval None
  */
void System_Init(void)
{
    /* 1. 基础系统初始化 */
    SystemInit();

    /* 2. 初始化SysTick定时器 (1ms中断) */
    SysTick_Init();

    /* 3. 初始化串口通信 */
    USART_Init();

    /* 4. 初始化按键模块 */
    Key_Init();

    /* 5. 初始化OLED显示 */
    OLED_Init();
    Delay_ms(100);

    /* 6. 初始化ADC+DMA多通道采集模块 - 高性能版 */
    ADC_Config_t adc_config = {
        .sample_rate = 500000,          // 500kSPS (高性能设置)
        .resolution = ADC_RESOLUTION_12BIT,
        .mode = ADC_MODE_CONTINUOUS,    // 连续采样模式
        .enable_filter = true           // 启用数字滤波
    };

    if (ADC_Init(&adc_config) != 0) {
        // ADC初始化失败处理
        while(1);
    }
    g_adc_config = adc_config;

    /* 7. 初始化14位并行ADC接口模块 - LTC2246高性能版 */
    ParallelADC_Config_t parallel_config = {
        .max_sample_rate = 2000000,     // 2MSPS (高性能设置)
        .data_width = 14,               // 14位数据 (LTC2246)
        .enable_data_valid = true,      // 启用数据有效信号
        .enable_overflow_detect = true, // 启用溢出检测
        .trigger_edge = PARALLEL_ADC_TRIGGER_RISING // 上升沿触发
    };

    if (ParallelADC_Init(&parallel_config) != 0) {
        // 并行ADC初始化失败处理
        while(1);
    }
    g_parallel_config = parallel_config;

    /* 8. 初始化DDS波形发生器 - G题专用 */
    DDS_Config_t dds_config = {
        .frequency = 1000,              // 默认1kHz
        .amplitude = 2048,              // 默认幅度
        .phase = 0,                     // 默认相位
        .waveform = DDS_WAVEFORM_SINE,  // 默认正弦波
        .sample_rate = 100000           // 100kSPS采样率
    };

    if (DDS_Init(&dds_config) != 0) {
        // DDS初始化失败处理
        while(1);
    }
    g_dds_config = dds_config;

    /* 9. 初始化自动增益控制 */
    AGC_Config_t agc_config = {
        .target_amplitude = 2.0f,       // 目标幅度2V
        .gain_min = 1,                  // 最小增益
        .gain_max = 8,                  // 最大增益
        .current_gain = 1,              // 当前增益
        .update_rate = 10               // 更新频率10Hz
    };

    if (AGC_Init(&agc_config) != 0) {
        // AGC初始化失败处理
        while(1);
    }
    g_agc_config = agc_config;

    /* 10. 启动DDS定时器 - 硬件精确触发 */
    DDS_Start();

    /* 11. 启动ADC采样 */
    ADC_Start();

    /* 12. 启动并行ADC */
    ParallelADC_Start();

#if DEBUG_ENABLE
    /* 13. 输出初始化完成信息 */
    sprintf(debug_buffer, "\r\n=== STM32F407 Circuit Explorer ===\r\n");
    USART_SendString(debug_buffer);
    sprintf(debug_buffer, "ADC Sample Rate: %lu kSPS\r\n", g_adc_config.sample_rate/1000);
    USART_SendString(debug_buffer);
    sprintf(debug_buffer, "DDS Frequency: %lu Hz\r\n", g_dds_config.frequency);
    USART_SendString(debug_buffer);
    sprintf(debug_buffer, "System Ready!\r\n\r\n");
    USART_SendString(debug_buffer);
#endif
}

/**
  * @brief  系统状态机处理函数
  * @param  None
  * @retval None
  */
void System_StateMachine(void)
{
    static uint32_t state_counter = 0;

    // 简单的状态机实现
    switch(state_counter % 4) {
        case 0:
            // 状态0：正常运行
            break;
        case 1:
            // 状态1：数据处理
            break;
        case 2:
            // 状态2：显示更新
            break;
        case 3:
            // 状态3：系统监控
            break;
    }

    state_counter++;
}

/**
  * @brief  自动增益控制更新函数
  * @param  signal_amplitude: 信号幅度
  * @retval None
  */
void AGC_Update(float signal_amplitude)
{
    // 简化的AGC算法
    if (signal_amplitude < g_agc_config.target_amplitude * 0.8f) {
        // 信号太小，增加增益
        if (g_agc_config.current_gain < g_agc_config.gain_max) {
            g_agc_config.current_gain++;
            // 这里应该调用硬件增益控制函数
        }
    } else if (signal_amplitude > g_agc_config.target_amplitude * 1.2f) {
        // 信号太大，减少增益
        if (g_agc_config.current_gain > g_agc_config.gain_min) {
            g_agc_config.current_gain--;
            // 这里应该调用硬件增益控制函数
        }
    }
}

/**
  * @brief  更新G题DDS输出 - 硬件定时器触发版
  * @param  None
  * @retval None
  * @note   现在由TIM6中断触发，提供精确的时序控制
  */
void G_DDS_UpdateOutput(void)
{
    // 硬件定时器触发版本 - 在TIM6中断中调用
    // 生成新的采样点并输出到DAC
    uint16_t dac_sample = G_DDS_GenerateSample();
    DAC8552_Write(DAC8552_CHANNEL_A, dac_sample);
}

/**
  * @brief  计算两信号间相位差 - 高精度互相关算法
  * @param  signal1: 参考信号
  * @param  signal2: 测试信号
  * @param  length: 信号长度
  * @retval 相位差 (度)
  */
float DSP_CalculatePhase(float* signal1, float* signal2, uint16_t length)
{
    // 使用互相关算法计算相位差，精度比过零点检测高5-10倍
    float max_correlation = 0.0f;
    int32_t best_delay = 0;

    // 计算互相关函数
    for (int32_t delay = -(int32_t)length/4; delay <= (int32_t)length/4; delay++) {
        float correlation = 0.0f;
        uint16_t valid_samples = 0;

        for (uint16_t i = 0; i < length; i++) {
            int32_t j = (int32_t)i + delay;
            if (j >= 0 && j < (int32_t)length) {
                correlation += signal1[i] * signal2[j];
                valid_samples++;
            }
        }

        if (valid_samples > 0) {
            correlation /= valid_samples;
            if (fabsf(correlation) > fabsf(max_correlation)) {
                max_correlation = correlation;
                best_delay = delay;
            }
        }
    }

    // 将延迟转换为相位差(度)
    float phase_diff = (float)best_delay * 360.0f / length;

    // 限制在-180到180度范围内
    while (phase_diff > 180.0f) phase_diff -= 360.0f;
    while (phase_diff < -180.0f) phase_diff += 360.0f;

    return phase_diff;
}

/**
  * @brief  高精度FFT实现 - 使用CMSIS-DSP库
  * @param  input: 输入信号
  * @param  output: 输出频谱幅度
  * @param  length: 信号长度 (必须是2的幂)
  * @retval None
  */
void DSP_SimpleFFT(float* input, float* output, uint16_t length)
{
    // 使用CMSIS-DSP库进行高精度FFT计算
    static arm_rfft_fast_instance_f32 fft_instance;
    static bool fft_initialized = false;
    static float fft_buffer[2048]; // 支持最大1024点FFT (复数需要2倍空间)

    // 初始化FFT实例
    if (!fft_initialized) {
        arm_rfft_fast_init_f32(&fft_instance, length);
        fft_initialized = true;
    }

    // 执行实数FFT
    arm_rfft_fast_f32(&fft_instance, input, fft_buffer, 0);

    // 计算幅度谱
    arm_cmplx_mag_f32(fft_buffer, output, length/2);

    // 归一化
    for (uint16_t i = 0; i < length/2; i++) {
        output[i] /= length;
    }
}

/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 系统时钟已经在SystemInit()中配置 */
    /* 这里可以添加额外的时钟配置代码 */
}

/**
  * @brief  频率扫描单点测试函数
  * @param  frequency: 测试频率
  * @retval None
  */
void FreqSweep_SinglePoint(uint32_t frequency)
{
    // 设置DDS频率
    DDS_SetFrequency(frequency);

    // 等待稳定
    for (volatile uint32_t i = 0; i < 100000; i++);

    // 采集数据
    float input_signal[256];
    float output_signal[256];

    // 这里应该实际采集信号数据
    // 简化实现：生成测试数据
    for (uint16_t i = 0; i < 256; i++) {
        input_signal[i] = sinf(2.0f * PI * frequency * i / 100000.0f);
        output_signal[i] = 0.8f * sinf(2.0f * PI * frequency * i / 100000.0f + PI/4);
    }

    // 计算幅度比和相位差
    float magnitude_ratio = 0.8f; // 简化计算
    float phase_diff = DSP_CalculatePhase(input_signal, output_signal, 256);

    // 转换为dB
    float magnitude_db = 20.0f * log10f(magnitude_ratio);

    // 显示结果
    sprintf(debug_buffer, "F:%luHz M:%.1fdB P:%.1f°", frequency, magnitude_db, phase_diff);
    OLED_ShowString(0, 32, debug_buffer, 8, 1);
    OLED_Refresh();

#if DEBUG_ENABLE
    sprintf(debug_buffer, "Freq: %lu Hz, Mag: %.2f dB, Phase: %.1f deg\r\n",
            frequency, magnitude_db, phase_diff);
    USART_SendString(debug_buffer);
#endif
}

/**
  * @brief  数据采集处理函数
  * @param  None
  * @retval None
  */
void DataAcquisition_GetData(void)
{
    // 简化的数据采集处理
    static uint32_t sample_counter = 0;

    // 控制采样率
    for (volatile uint32_t j = 0; j < 1000; j++);

    sample_counter++;

    // 每1000个样本处理一次
    if (sample_counter >= 1000) {
        sample_counter = 0;

        // 这里可以添加数据处理逻辑
    }
}

/**
  * @brief  系统显示更新函数
  * @param  None
  * @retval None
  */
void System_UpdateDisplay(void)
{
    static uint32_t display_mode = 0;

    // 循环显示不同信息
    switch(display_mode % 3) {
        case 0:
            // 显示频率信息
            sprintf(debug_buffer, "F:%luHz", g_dds_config.frequency);
            OLED_ShowString(0, 16, debug_buffer, 8, 1);
            break;

        case 1:
            // 显示增益信息
            sprintf(debug_buffer, "G:%d", g_agc_config.current_gain);
            OLED_ShowString(64, 16, debug_buffer, 8, 1);
            break;

        case 2:
            // 显示状态信息
            OLED_ShowString(0, 48, "Running...", 8, 1);
            break;
    }

    OLED_Refresh();
    display_mode++;
}

/**
  * @brief  毫秒延时函数
  * @param  ms: 延时毫秒数
  * @retval None
  */
void Delay(uint32_t ms)
{
    uint32_t tickstart = 0;
    tickstart = SysTick_GetTick();
    while((SysTick_GetTick() - tickstart) < ms)
    {
        // 防止编译器优化掉这个循环
        __NOP();
    }
}

/**
  * @brief  获取系统滴答计数
  * @param  None
  * @retval 当前滴答计数值
  */
uint32_t GetTick(void)
{
    return SysTick_GetTick();
}

/**
  * @brief  SysTick相关的空减计数函数，供模板代码引用
  */
void TimingDelay_Decrement(void)
{
    /* 已使用SysTick_GetTick实现延时，此处留空即可满足链接 */
}

/**
  * @brief  G题专用DDS采样生成函数
  * @param  None
  * @retval DAC采样值
  */
uint16_t G_DDS_GenerateSample(void)
{
    // 简化的DDS采样生成
    static uint32_t phase_accumulator = 0;
    static const uint16_t sine_table[256] = {
        // 256点正弦波表 (简化版本)
        2048, 2098, 2148, 2198, 2248, 2298, 2348, 2398,
        2447, 2496, 2545, 2594, 2642, 2690, 2737, 2784,
        // ... 这里应该包含完整的256点正弦波表
        // 为了简化，只显示前几个值
    };

    // 相位累加
    phase_accumulator += (g_dds_config.frequency * 256) / g_dds_config.sample_rate;

    // 获取表索引
    uint8_t table_index = (phase_accumulator >> 24) & 0xFF;

    // 返回DAC值
    return sine_table[table_index];
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  assert_failed: 发生参数错误时进入此函数
  * @param  file: 源文件名
  * @param  line: 行号
  */
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    (void)file;
    (void)line;

#if DEBUG_ENABLE
    sprintf(debug_buffer, "Assert failed: %s:%lu\r\n", file, line);
    USART_SendString(debug_buffer);
#endif

    while (1) {}
}
#endif

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
