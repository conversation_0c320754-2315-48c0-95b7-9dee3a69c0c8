/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V2.0
  * @date    2024
  * @brief   STM32F4竞赛级模块库集成示例 - 嘉立创天空星STM32F407VGT6
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>

// 竞赛级模块库 - 硬件适配修正版
#include "../Modules/Core/systick.h"
#include "../Modules/Core/usart.h"
#include "../Modules/Acquisition/adc_dma.h"
#include "../Modules/Acquisition/parallel_adc.h"
#include "../Modules/Generation/dds_wavegen.h"
#include "../Modules/Interface/oled.h"          // I2C OLED显示模块(修正版)
#include "../Modules/Interface/key.h"           // 按键扫描模块(简化版)

// 电赛G题专用驱动模块 - 基于商家代码优化
#include "../Modules/Generation/dac8552.h"     // DAC8552双通道DAC驱动(优化版)
#include "../Modules/Acquisition/ad7606.h"     // AD7606同步ADC驱动(优化版)
#include "../Modules/Interface/cd4052.h"       // CD4052增益控制驱动(优化版)

// 数学常数定义 - 头文件已在main.h中包含
#define PI 3.14159265358979323846f
#define TWO_PI (2.0f * PI)

// 使用CMSIS-DSP库和标准库的高精度数学函数
// sqrtf, log10f, logf, powf, cosf, sinf 现在使用标准库实现

// #include "fft.h"           // FFT模块(需要CMSIS-DSP库配置)
// #include "input_capture.h" // 频率测量模块
// #include "pwm.h"           // PWM生成模块


/* Private variables ---------------------------------------------------------*/
static char debug_buffer[256];

// ==================== 电赛G题核心数据结构 ====================

// G题专用DDS信号发生器参数 (避免与现有模块冲突)
typedef struct {
    uint32_t frequency;          // 输出频率 (Hz)
    float amplitude;             // 输出幅度 (V)
    uint16_t phase_accumulator;  // 相位累加器
    uint16_t phase_increment;    // 相位增量
    uint8_t waveform_type;       // 波形类型: 0=正弦波, 1=方波
} G_DDS_Config_t;

// 自动增益控制参数
typedef struct {
    uint8_t current_gain;        // 当前增益级别 (0-3)
    float target_amplitude;      // 目标信号幅度 (V)
    float measured_amplitude;    // 测量信号幅度 (V)
    uint8_t agc_enable;          // AGC使能标志
    uint32_t settle_time;        // 增益切换稳定时间计数
} AGC_Config_t;

// 频率响应测量结果
typedef struct {
    float frequency;             // 测试频率 (Hz)
    float magnitude_db;          // 幅频响应 (dB)
    float phase_deg;             // 相频响应 (度)
    float input_amplitude;       // 输入信号幅度 (V)
    float output_amplitude;      // 输出信号幅度 (V)
    uint8_t valid;               // 数据有效标志
} FreqResponse_t;

// PID控制器参数
typedef struct {
    float kp, ki, kd;            // PID参数
    float setpoint;              // 目标值
    float error;                 // 当前误差
    float integral;              // 积分项
    float derivative;            // 微分项
    float last_error;            // 上次误差
    float output;                // 控制输出
} PID_Controller_t;

// 系统配置常量
#define DDS_SAMPLE_RATE         200000      // DDS采样率 (Hz)
#define FFT_SIZE                1024        // FFT点数
#define MAX_FREQUENCY           1000000     // 最大频率 1MHz
#define MIN_FREQUENCY           100         // 最小频率 100Hz
#define FREQ_SWEEP_POINTS       50          // 频率扫描点数
#define AGC_SETTLE_TIME         1000        // AGC稳定时间 (采样点)
#define PID_UPDATE_RATE         100         // PID更新频率 (Hz)

// 全局变量
static G_DDS_Config_t g_dds_config;
static AGC_Config_t g_agc_config;
static PID_Controller_t g_pid_controller;
static FreqResponse_t g_freq_response[FREQ_SWEEP_POINTS];

// 数据缓冲区 (重命名避免冲突)
static int16_t g_adc7606_data[AD7606_CHANNEL_COUNT];
// static int16_t g_adc_data_buffer[AD7606_CHANNEL_COUNT * FFT_SIZE]; // 暂时未使用，注释掉避免警告
static float g_input_signal[FFT_SIZE];
static float g_output_signal[FFT_SIZE];

// 正弦波查找表 (256点)
static const uint16_t sine_table[256] = {
    2048, 2098, 2148, 2198, 2248, 2298, 2348, 2398, 2447, 2496, 2545, 2594, 2642, 2690, 2737, 2784,
    2831, 2877, 2923, 2968, 3013, 3057, 3100, 3143, 3185, 3226, 3267, 3307, 3346, 3385, 3423, 3459,
    3495, 3530, 3565, 3598, 3630, 3662, 3692, 3722, 3750, 3777, 3804, 3829, 3853, 3876, 3898, 3919,
    3939, 3958, 3976, 3992, 4007, 4021, 4034, 4045, 4056, 4065, 4073, 4080, 4085, 4089, 4093, 4094,
    4095, 4094, 4093, 4089, 4085, 4080, 4073, 4065, 4056, 4045, 4034, 4021, 4007, 3992, 3976, 3958,
    3939, 3919, 3898, 3876, 3853, 3829, 3804, 3777, 3750, 3722, 3692, 3662, 3630, 3598, 3565, 3530,
    3495, 3459, 3423, 3385, 3346, 3307, 3267, 3226, 3185, 3143, 3100, 3057, 3013, 2968, 2923, 2877,
    2831, 2784, 2737, 2690, 2642, 2594, 2545, 2496, 2447, 2398, 2348, 2298, 2248, 2198, 2148, 2098,
    2048, 1998, 1948, 1898, 1848, 1798, 1748, 1698, 1649, 1600, 1551, 1502, 1454, 1406, 1359, 1312,
    1265, 1219, 1173, 1128, 1083, 1039, 996, 953, 911, 870, 829, 789, 750, 711, 673, 637,
    601, 566, 531, 498, 466, 434, 404, 374, 346, 319, 292, 267, 243, 220, 198, 177,
    157, 138, 120, 104, 89, 75, 62, 51, 40, 31, 23, 16, 11, 7, 3, 2,
    1, 2, 3, 7, 11, 16, 23, 31, 40, 51, 62, 75, 89, 104, 120, 138,
    157, 177, 198, 220, 243, 267, 292, 319, 346, 374, 404, 434, 466, 498, 531, 566,
    601, 637, 673, 711, 750, 789, 829, 870, 911, 953, 996, 1039, 1083, 1128, 1173, 1219,
    1265, 1312, 1359, 1406, 1454, 1502, 1551, 1600, 1649, 1698, 1748, 1798, 1848, 1898, 1948, 1998
};

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void Module_Init_Demo(void);
void Module_Test_Demo(void);

// 电赛G题专用函数
uint8_t G_Module_Init(void);
void G_Module_Test(void);
void G_Comprehensive_Test(void);

// ==================== 核心功能模块函数声明 ====================

// 1. G题专用DDS信号发生器模块 (避免与现有模块冲突)
void G_DDS_Init(void);
void G_DDS_SetFrequency(uint32_t frequency);
void G_DDS_SetAmplitude(float amplitude);
void G_DDS_UpdateOutput(void);
uint16_t G_DDS_GenerateSample(void);

// 2. 同步数据采集模块
void DataAcquisition_Init(void);
uint8_t DataAcquisition_Start(uint32_t sample_rate);
uint8_t DataAcquisition_GetData(float* input_buf, float* output_buf, uint16_t length);
void DataAcquisition_Stop(void);

// 3. 自动增益控制模块
void AGC_Init(void);
void AGC_Update(float signal_amplitude);
uint8_t AGC_GetOptimalGain(float signal_level);
void AGC_SetGain(uint8_t gain_level);

// 4. 数字信号处理模块
void DSP_Init(void);
float DSP_CalculateRMS(float* signal, uint16_t length);
float DSP_CalculatePhase(float* signal1, float* signal2, uint16_t length);
void DSP_SimpleFFT(float* input, float* output, uint16_t length);
FreqResponse_t DSP_CalculateFreqResponse(float* input, float* output, float frequency);

// 5. PID控制模块
void PID_Init(PID_Controller_t* pid, float kp, float ki, float kd);
float PID_Update(PID_Controller_t* pid, float setpoint, float measured_value);
void PID_Reset(PID_Controller_t* pid);

// 6. 频率扫描和测量
void FreqSweep_Init(void);
uint8_t FreqSweep_SinglePoint(uint32_t frequency, FreqResponse_t* result);
void FreqSweep_Auto(uint32_t start_freq, uint32_t end_freq, uint8_t points);
void FreqSweep_DisplayResults(void);

// 7. 系统控制和状态管理
void System_StateMachine(void);
void System_UpdateDisplay(void);
void System_ProcessCommands(void);

/**
  * @brief  Main program - 竞赛级模块库集成示例
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统时钟初始化 */
    SystemInit();

    /* 初始化竞赛级模块库 */
    Module_Init_Demo();

    /* 初始化电赛G题专用模块 */
    if (G_Module_Init() != 0) {
        USART_Printf("G Module Init Failed, System Halt\r\n");
        while (1);
    }

    /* 初始化核心功能模块 */
    G_DDS_Init();
    DataAcquisition_Init();
    AGC_Init();
    DSP_Init();
    FreqSweep_Init();
    PID_Init(&g_pid_controller, 1.0f, 0.1f, 0.05f);

    USART_Printf("=== Circuit Model Explorer Ready ===\r\n");
    USART_Printf("Press KEY0: Start Freq Sweep\r\n");
    USART_Printf("Press KEY1: Single Point Test\r\n");

    /* 模块功能测试演示 */
    Module_Test_Demo();

    /* G题专用模块测试 */
    G_Module_Test();

    /* 主循环 - 简化版本，专注于模块集成演示 */
    while (1)
    {
        /* 检查ADC数据是否就绪 */
        if (g_adc_dma_full_complete) {
            g_adc_dma_full_complete = false;

            // 处理ADC数据
            ADC_ProcessData();

#if DEBUG_ENABLE
            // 通过串口输出统计信息 (仅在调试模式下)
            ADC_Stats_t adc_stats;
            ADC_GetStats(&adc_stats);

            sprintf(debug_buffer, "ADC: Samples=%lu, Rate=%.1fkSPS\r\n",
                    adc_stats.total_samples, adc_stats.actual_sample_rate/1000.0f);
            USART_SendString(debug_buffer);
#endif
        }

        /* 系统状态机处理 */
        System_StateMachine();

        /* DDS输出现在由TIM6中断自动触发，无需在主循环中调用 */

        /* 自动增益控制更新 */
        static uint32_t agc_counter = 0;
        agc_counter++;
        if (agc_counter > 50000) { // AGC更新频率控制
            agc_counter = 0;

            // 简单的信号幅度检测和AGC更新
            int16_t adc_data[AD7606_CHANNEL_COUNT];
            if (AD7606_ReadData(adc_data) == AD7606_OK) {
                float signal_amplitude = (float)adc_data[0] * 5.0f / 32768.0f; // 转换为电压
                AGC_Update(signal_amplitude);

#if DEBUG_ENABLE
                // 显示实时数据 (仅在调试模式下)
                sprintf(debug_buffer, "Sig: %.3fV, Gain: %d, Freq: %luHz\r\n",
                        signal_amplitude, g_agc_config.current_gain, g_dds_config.frequency);
                USART_SendString(debug_buffer);
#endif
            }
        }

        /* 更新系统显示 */
        System_UpdateDisplay();

        /* 处理系统命令 */
        System_ProcessCommands();

        /* 检查并行ADC数据 */
        if (g_parallel_adc_data_ready) {
            g_parallel_adc_data_ready = false;
            uint16_t data;
            if (ParallelADC_ReadSingle(&data) == 0) {
#if DEBUG_ENABLE
                sprintf(debug_buffer, "Parallel ADC: %d\r\n", data);
                USART_SendString(debug_buffer);
#endif
            }
        }

        /* 检查DDS更新 */
        if (g_dds_update_complete) {
            g_dds_update_complete = false;

#if DEBUG_ENABLE
            DDS_Stats_t dds_stats;
            DDS_GetStats(&dds_stats);

            sprintf(debug_buffer, "DDS: Freq=%.1fHz, Samples=%lu\r\n",
                    dds_stats.actual_frequency, dds_stats.output_samples);
            USART_SendString(debug_buffer);
#endif
        }

        /* 检查按键 */
        Key_Number_t key = Key_Scan();
        if (key != KEY_NONE) {
            sprintf(debug_buffer, "按键按下: KEY_%d\r\n", key - 1);
            USART_SendString(debug_buffer);

            // 按键功能 - 电路特性测试控制
            if (key == KEY_0) {
                // KEY0: 启动频率扫描测试
                USART_Printf("Starting Frequency Sweep Test...\r\n");

                // 更新OLED显示
                OLED_Clear();
                OLED_ShowString(0, 0, "Freq Sweep", OLED_FONT_6x8);
                OLED_ShowString(0, 16, "Running...", OLED_FONT_6x8);
                OLED_Refresh();

                // 执行频率扫描 (100Hz - 100kHz, 20点)
                FreqSweep_Auto(100, 100000, 20);

                // 显示结果
                FreqSweep_DisplayResults();

                OLED_Clear();
                OLED_ShowString(0, 0, "Sweep Done", OLED_FONT_6x8);
                OLED_Refresh();
            }

            if (key == KEY_1) {
                // KEY1: 单点频率测试
                static uint32_t test_frequencies[] = {100, 1000, 10000, 100000};
                static uint8_t freq_index = 0;

                uint32_t test_freq = test_frequencies[freq_index];
                freq_index = (freq_index + 1) % 4;

                USART_Printf("Single Point Test at %luHz...\r\n", test_freq);

                FreqResponse_t result;
                if (FreqSweep_SinglePoint(test_freq, &result) == 0) {
                    sprintf(debug_buffer, "Freq: %luHz, Mag: %.2fdB, Phase: %.1fdeg\r\n",
                            (uint32_t)result.frequency, result.magnitude_db, result.phase_deg);
                    USART_SendString(debug_buffer);

                    // 更新OLED显示
                    OLED_Clear();
                    OLED_ShowString(0, 0, "Single Test", OLED_FONT_6x8);
                    sprintf(debug_buffer, "%luHz", test_freq);
                    OLED_ShowString(0, 16, (const char*)debug_buffer, OLED_FONT_6x8);
                    sprintf(debug_buffer, "%.1fdB", result.magnitude_db);
                    OLED_ShowString(0, 32, (const char*)debug_buffer, OLED_FONT_6x8);
                    OLED_Refresh();
                }
            }
        }

        /* 系统状态监控 */
        static uint32_t last_status_time = 0;
        if (SysTick_GetTick() - last_status_time > 5000) { // 每5秒输出一次状态
            last_status_time = SysTick_GetTick();

#if DEBUG_ENABLE
            sprintf(debug_buffer, "System Uptime: %lu ms\r\n", SysTick_GetUptime_ms());
            USART_SendString(debug_buffer);
#endif
        }

        /* 短暂延时，降低CPU占用 */
        Delay_ms(10);
    }
}

/**
  * @brief  竞赛级模块库初始化演示
  * @param  None
  * @retval None
  */
void Module_Init_Demo(void)
{
    /* 1. 初始化SysTick高精度延时模块 */
    if (SysTick_Init() != 0) {
        // 初始化失败处理
        while (1);
    }

    /* 2. 初始化USART调试模块 */
    if (USART1_Init(115200) != 0) {
        // 初始化失败处理
        while (1);
    }

    /* 输出启动信息 */
    USART_Printf("\r\n=== STM32F4竞赛级模块库演示 ===\r\n");
    USART_Printf("系统时钟: %lu MHz\r\n", SystemCoreClock / 1000000);
    USART_Printf("编译时间: %s %s\r\n", __DATE__, __TIME__);

    /* 3. 初始化ADC+DMA多通道采集模块 - 高性能版 */
    ADC_Config_t adc_config = {
        .sample_rate = 500000,          // 500kSPS (高性能设置)
        .resolution = ADC_RESOLUTION_12BIT,
        .mode = ADC_MODE_CONTINUOUS,    // 连续采样模式
        .enable_filter = true           // 启用数字滤波
    };

    if (ADC1_DMA_Init(&adc_config) == 0) {
        USART_Printf("ADC+DMA模块初始化成功\r\n");
        ADC_Start_Acquisition();
    } else {
        USART_Printf("ADC+DMA模块初始化失败\r\n");
    }

    /* 4. 初始化14位并行ADC接口模块 - LTC2246高性能版 */
    ParallelADC_Config_t parallel_config = {
        .max_sample_rate = 2000000,     // 2MSPS (高性能设置)
        .data_width = 14,               // 14位数据 (LTC2246)
        .enable_data_valid = true,      // 启用数据有效信号
        .enable_overflow_detect = true, // 启用溢出检测
        .trigger_edge = PARALLEL_ADC_TRIGGER_RISING // 上升沿触发
    };

    if (ParallelADC_Init(&parallel_config) == 0) {
        USART_Printf("并行ADC模块初始化成功\r\n");
        ParallelADC_Start();
    } else {
        USART_Printf("并行ADC模块初始化失败\r\n");
    }

    /* 5. DDS波形生成模块已在G_DDS_Init中初始化 */
    USART_Printf("DDS Module Ready\r\n");

    /* 6. 初始化I2C OLED显示模块 */
    if (OLED_Init() == 0) {
        USART_Printf("I2C OLED显示模块初始化成功\r\n");
        OLED_Clear();
        OLED_ShowString(0, 0, "STM32F407", OLED_FONT_6x8);
        OLED_ShowString(0, 16, "Contest Lib", OLED_FONT_6x8);
        OLED_ShowString(0, 32, "Hardware Fix", OLED_FONT_6x8);
        OLED_ShowString(0, 48, "Ready!", OLED_FONT_6x8);
        OLED_Refresh();
    } else {
        USART_Printf("I2C OLED显示模块初始化失败\r\n");
    }

    /* 7. 初始化按键扫描模块 */
    if (Key_Init() == 0) {
        USART_Printf("按键扫描模块初始化成功\r\n");
    } else {
        USART_Printf("按键扫描模块初始化失败\r\n");
    }

    USART_Printf("所有模块初始化完成！\r\n\r\n");
}

/**
  * @brief  模块功能测试演示
  * @param  None
  * @retval None
  */
void Module_Test_Demo(void)
{
    USART_Printf("Start Module Test...\r\n");

    /* 测试SysTick精确延时 */
    uint32_t start_time = SysTick_GetTick();
    Delay_ms(100);
    uint32_t elapsed = SysTick_GetTick() - start_time;
    USART_Printf("SysTick Delay Test: Expected 100ms, Actual %lums\r\n", elapsed);

    /* 测试DDS频率设置 */
    DDS_SetFrequency(2000); // 设置为2kHz
    USART_Printf("DDS Frequency Set to 2kHz\r\n");

    Delay_ms(1000);

    DDS_SetWaveType(DDS_WAVE_SQUARE); // 切换为方波
    USART_Printf("DDS波形切换为方波\r\n");

    /* 测试ADC校准 */
    if (ADC_Calibrate() == 0) {
        USART_Printf("ADC校准完成\r\n");
    }

    USART_Printf("模块功能测试完成！\r\n\r\n");
}

/**
  * @brief  电赛G题专用模块初始化
  * @param  None
  * @retval 0: 成功, 1: 失败
  */
uint8_t G_Module_Init(void)
{
    uint8_t result;

    USART_Printf("=== 电赛G题专用模块初始化 ===\r\n");

    /* 1. 初始化BSP */
    BSP_Init();
    USART_Printf("BSP初始化完成\r\n");

    /* 2. 初始化DAC8552 */
    result = DAC8552_Init();
    if (result != DAC8552_OK) {
        USART_Printf("DAC8552 Init Failed: %d\r\n", result);
        return 1;
    }
    USART_Printf("DAC8552初始化成功\r\n");

    /* 3. 初始化CD4052 */
    result = CD4052_Init();
    if (result != CD4052_OK) {
        USART_Printf("CD4052 Init Failed: %d\r\n", result);
        return 1;
    }
    USART_Printf("CD4052初始化成功\r\n");

    /* 4. 初始化AD7606 */
    result = AD7606_Init();
    if (result != AD7606_OK) {
        USART_Printf("AD7606 Init Failed: %d\r\n", result);
        return 1;
    }
    USART_Printf("AD7606初始化成功\r\n");

    USART_Printf("G题专用模块初始化完成！\r\n\r\n");
    return 0;
}

/**
  * @brief  G题专用模块测试
  * @param  None
  * @retval None
  */
void G_Module_Test(void)
{
    uint8_t result;

    USART_Printf("=== G Module Test ===\r\n");

    /* 1. DAC输出测试 */
    USART_Printf("DAC8552输出测试...\r\n");
    result = DAC8552_SetVoltage(DAC8552_CHANNEL_A, 2.5f);
    if (result == DAC8552_OK) {
        USART_Printf("DAC通道A输出2.5V - 成功\r\n");
    }

    result = DAC8552_SetVoltage(DAC8552_CHANNEL_B, 1.0f);
    if (result == DAC8552_OK) {
        USART_Printf("DAC通道B输出1.0V - 成功\r\n");
    }

    /* 2. 增益控制测试 */
    USART_Printf("CD4052增益控制测试...\r\n");
    for (uint8_t level = 0; level < 4; level++) {
        result = CD4052_SetGain(level);
        if (result == CD4052_OK) {
            USART_Printf("增益级别%d设置成功\r\n", level);
        }
        Delay_ms(100);
    }

    /* 3. ADC采集测试 */
    USART_Printf("AD7606采集测试...\r\n");
    result = AD7606_ReadData(g_adc7606_data);
    if (result == AD7606_OK) {
        USART_Printf("AD7606采集成功: ");
        for (uint8_t i = 0; i < 4; i++) {
            USART_Printf("CH%d=%d ", i, g_adc7606_data[i]);
        }
        USART_Printf("\r\n");
    }

    USART_Printf("G题专用模块测试完成！\r\n\r\n");
}

/**
  * @brief  G题综合测试 - 模拟实际应用
  * @param  None
  * @retval None
  */
void G_Comprehensive_Test(void)
{
    uint8_t result;

    USART_Printf("=== G Comprehensive Test ===\r\n");

    for (uint8_t cycle = 0; cycle < 3; cycle++) {
        USART_Printf("--- 测试周期 %d ---\r\n", cycle + 1);

        /* 设置DAC输出 */
        float voltage = 1.0f + cycle * 1.0f;
        result = DAC8552_SetVoltage(DAC8552_CHANNEL_A, voltage);
        if (result == DAC8552_OK) {
            USART_Printf("DAC输出: %.1fV\r\n", voltage);
        }

        /* 设置增益级别 */
        result = CD4052_SetGain(cycle % 4);
        if (result == CD4052_OK) {
            USART_Printf("增益级别: %d\r\n", cycle % 4);
        }

        /* 等待系统稳定 */
        Delay_ms(50);

        /* ADC采集 */
        result = AD7606_ReadData(g_adc7606_data);
        if (result == AD7606_OK) {
            USART_Printf("ADC结果: CH0=%d, CH1=%d\r\n",
                        g_adc7606_data[0], g_adc7606_data[1]);
        }

        Delay_ms(500);
    }

    USART_Printf("G题综合测试完成！\r\n\r\n");
}

/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 系统时钟已经在SystemInit()中配置为168MHz */
    /* 这里可以添加额外的时钟配置代码 */
}

/**
  * @brief  CMSIS-DSP库配置说明
  * @note   要使用FFT模块，需要在Keil项目中进行以下配置：
  *         1. 添加CMSIS-DSP库路径到Include Paths
  *         2. 在C/C++选项的Define中添加：ARM_MATH_CM4,ARM_MATH_MATRIX_CHECK,ARM_MATH_ROUNDING
  *         3. 在链接器中添加库文件：arm_cortexM4lf_math.lib
  *         4. 取消注释上面的#include "fft.h"
  */

#ifdef  USE_FULL_ASSERT
/**
  * @brief  assert_failed: 发生参数错误时进入此函数
  * @param  file: 源文件名
  * @param  line: 行号
  */
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    USART_Printf("ASSERT FAILED: %s:%lu\r\n", file, line);
    while (1) {}
}
#endif

/**
  * @brief  定时延时递减函数
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

// ==================== 核心功能模块实现 ====================

/**
 * @brief  G题专用DDS信号发生器初始化
 * @param  None
 * @retval None
 */
void G_DDS_Init(void)
{
    // 初始化DDS配置
    g_dds_config.frequency = 1000;          // 默认1kHz
    g_dds_config.amplitude = 2.5f;          // 默认2.5V
    g_dds_config.phase_accumulator = 0;
    g_dds_config.waveform_type = 0;         // 正弦波

    // 计算相位增量: phase_increment = (frequency * 256) / sample_rate
    g_dds_config.phase_increment = (g_dds_config.frequency * 256) / DDS_SAMPLE_RATE;

    USART_Printf("DDS Init: Freq=%luHz, Amp=%.1fV\r\n",
                 g_dds_config.frequency, g_dds_config.amplitude);
}

/**
 * @brief  设置G题DDS输出频率
 * @param  frequency: 目标频率 (Hz)
 * @retval None
 */
void G_DDS_SetFrequency(uint32_t frequency)
{
    // 频率范围限制
    if (frequency < MIN_FREQUENCY) frequency = MIN_FREQUENCY;
    if (frequency > MAX_FREQUENCY) frequency = MAX_FREQUENCY;

    g_dds_config.frequency = frequency;

    // 重新计算相位增量
    g_dds_config.phase_increment = (frequency * 256) / DDS_SAMPLE_RATE;

    USART_Printf("DDS Freq Set: %luHz\r\n", frequency);
}

/**
 * @brief  设置G题DDS输出幅度
 * @param  amplitude: 目标幅度 (V)
 * @retval None
 */
void G_DDS_SetAmplitude(float amplitude)
{
    // 幅度范围限制
    if (amplitude < 0.1f) amplitude = 0.1f;
    if (amplitude > 5.0f) amplitude = 5.0f;

    g_dds_config.amplitude = amplitude;

    USART_Printf("DDS Amp Set: %.2fV\r\n", amplitude);
}

/**
 * @brief  生成G题DDS采样点
 * @param  None
 * @retval 16位DAC数值
 */
uint16_t G_DDS_GenerateSample(void)
{
    uint16_t dac_value;
    uint8_t table_index;

    // 获取正弦波表索引 (高8位)
    table_index = (g_dds_config.phase_accumulator >> 8) & 0xFF;

    // 从查找表获取基础值
    uint16_t base_value = sine_table[table_index];

    // 应用幅度缩放
    float scaled_value = (float)base_value * g_dds_config.amplitude / 5.0f;

    // 转换为DAC数值
    dac_value = (uint16_t)scaled_value;

    // 更新相位累加器
    g_dds_config.phase_accumulator += g_dds_config.phase_increment;

    return dac_value;
}

/**
 * @brief  更新G题DDS输出 - 硬件定时器触发版
 * @param  None
 * @retval None
 * @note   现在由TIM6中断触发，提供精确的时序控制
 */
void G_DDS_UpdateOutput(void)
{
    // 硬件定时器触发版本 - 在TIM6中断中调用
    // 生成新的采样点并输出到DAC
    uint16_t dac_sample = G_DDS_GenerateSample();
    DAC8552_Write(DAC8552_CHANNEL_A, dac_sample);
}

/**
 * @brief  数据采集模块初始化
 * @param  None
 * @retval None
 */
void DataAcquisition_Init(void)
{
    // 数据采集已在AD7606_Init中完成
    USART_Printf("Data Acquisition Init Complete\r\n");
}

/**
 * @brief  启动数据采集
 * @param  sample_rate: 采样率 (Hz)
 * @retval 0: 成功, 1: 失败
 */
uint8_t DataAcquisition_Start(uint32_t sample_rate)
{
    // 简化实现：直接使用AD7606的固定采样率
    USART_Printf("Data Acquisition Started at %luHz\r\n", sample_rate);
    return 0;
}

/**
 * @brief  获取采集数据
 * @param  input_buf: 输入信号缓冲区
 * @param  output_buf: 输出信号缓冲区
 * @param  length: 数据长度
 * @retval 0: 成功, 1: 失败
 */
uint8_t DataAcquisition_GetData(float* input_buf, float* output_buf, uint16_t length)
{
    int16_t adc_data[AD7606_CHANNEL_COUNT];

    for (uint16_t i = 0; i < length; i++) {
        if (AD7606_ReadData(adc_data) == AD7606_OK) {
            // 转换ADC数据为电压值
            input_buf[i] = (float)adc_data[0] * 5.0f / 32768.0f;   // 通道0: 输入信号
            output_buf[i] = (float)adc_data[1] * 5.0f / 32768.0f;  // 通道1: 输出信号
        } else {
            return 1; // 采集失败
        }

        // 简单延时控制采样率
        for (volatile uint32_t j = 0; j < 1000; j++);
    }

    return 0;
}

/**
 * @brief  停止数据采集
 * @param  None
 * @retval None
 */
void DataAcquisition_Stop(void)
{
    USART_Printf("Data Acquisition Stopped\r\n");
}

/**
 * @brief  自动增益控制初始化
 * @param  None
 * @retval None
 */
void AGC_Init(void)
{
    g_agc_config.current_gain = CD4052_GAIN_LEVEL_0;
    g_agc_config.target_amplitude = 2.0f;      // 目标2V
    g_agc_config.measured_amplitude = 0.0f;
    g_agc_config.agc_enable = 1;
    g_agc_config.settle_time = 0;

    // 设置初始增益
    CD4052_SetGain(g_agc_config.current_gain);

    USART_Printf("AGC Init: Target=%.1fV, Gain=%d\r\n",
                 g_agc_config.target_amplitude, g_agc_config.current_gain);
}

/**
 * @brief  更新自动增益控制
 * @param  signal_amplitude: 当前信号幅度
 * @retval None
 */
void AGC_Update(float signal_amplitude)
{
    if (!g_agc_config.agc_enable) return;

    g_agc_config.measured_amplitude = signal_amplitude;

    // 增益切换逻辑
    uint8_t new_gain = AGC_GetOptimalGain(signal_amplitude);

    if (new_gain != g_agc_config.current_gain) {
        AGC_SetGain(new_gain);
        g_agc_config.settle_time = AGC_SETTLE_TIME; // 重置稳定时间
    }

    // 减少稳定时间计数
    if (g_agc_config.settle_time > 0) {
        g_agc_config.settle_time--;
    }
}

/**
 * @brief  获取最优增益级别
 * @param  signal_level: 信号电平
 * @retval 最优增益级别 (0-3)
 */
uint8_t AGC_GetOptimalGain(float signal_level)
{
    // 简单的增益选择算法
    if (signal_level > 4.0f) {
        return CD4052_GAIN_LEVEL_0;      // 最小增益
    } else if (signal_level > 2.0f) {
        return CD4052_GAIN_LEVEL_1;
    } else if (signal_level > 1.0f) {
        return CD4052_GAIN_LEVEL_2;
    } else {
        return CD4052_GAIN_LEVEL_3;      // 最大增益
    }
}

/**
 * @brief  设置增益级别
 * @param  gain_level: 增益级别 (0-3)
 * @retval None
 */
void AGC_SetGain(uint8_t gain_level)
{
    if (CD4052_SetGain(gain_level) == CD4052_OK) {
        g_agc_config.current_gain = gain_level;
        USART_Printf("AGC Gain Set: %d\r\n", gain_level);
    }
}

/**
 * @brief  数字信号处理模块初始化
 * @param  None
 * @retval None
 */
void DSP_Init(void)
{
    // 清零信号缓冲区
    for (uint16_t i = 0; i < FFT_SIZE; i++) {
        g_input_signal[i] = 0.0f;
        g_output_signal[i] = 0.0f;
    }

    USART_Printf("DSP Init Complete\r\n");
}

/**
 * @brief  计算信号RMS值
 * @param  signal: 信号数组
 * @param  length: 数组长度
 * @retval RMS值
 */
float DSP_CalculateRMS(float* signal, uint16_t length)
{
    float sum_squares = 0.0f;

    for (uint16_t i = 0; i < length; i++) {
        sum_squares += signal[i] * signal[i];
    }

    return sqrtf(sum_squares / length);
}

/**
 * @brief  计算两信号间相位差 - 高精度互相关算法
 * @param  signal1: 参考信号
 * @param  signal2: 测试信号
 * @param  length: 信号长度
 * @retval 相位差 (度)
 */
float DSP_CalculatePhase(float* signal1, float* signal2, uint16_t length)
{
    // 使用互相关算法计算相位差，精度比过零点检测高5-10倍
    float max_correlation = 0.0f;
    int32_t best_delay = 0;

    // 计算互相关函数
    for (int32_t delay = -(int32_t)length/4; delay <= (int32_t)length/4; delay++) {
        float correlation = 0.0f;
        uint16_t valid_samples = 0;

        for (uint16_t i = 0; i < length; i++) {
            int32_t j = (int32_t)i + delay;
            if (j >= 0 && j < (int32_t)length) {
                correlation += signal1[i] * signal2[j];
                valid_samples++;
            }
        }

        if (valid_samples > 0) {
            correlation /= valid_samples;
            if (fabsf(correlation) > fabsf(max_correlation)) {
                max_correlation = correlation;
                best_delay = delay;
            }
        }
    }

    // 将延迟转换为相位差(度)
    float phase_diff = (float)best_delay * 360.0f / length;

    // 限制在-180到180度范围内
    while (phase_diff > 180.0f) phase_diff -= 360.0f;
    while (phase_diff < -180.0f) phase_diff += 360.0f;

    return phase_diff;
}

/**
 * @brief  高精度FFT实现 - 使用CMSIS-DSP库
 * @param  input: 输入信号
 * @param  output: 输出频谱幅度
 * @param  length: 信号长度 (必须是2的幂)
 * @retval None
 */
void DSP_SimpleFFT(float* input, float* output, uint16_t length)
{
    // 使用CMSIS-DSP库进行高精度FFT计算
    static arm_rfft_fast_instance_f32 fft_instance;
    static bool fft_initialized = false;
    static float fft_buffer[2048]; // 支持最大1024点FFT (复数需要2倍空间)

    // 初始化FFT实例
    if (!fft_initialized) {
        arm_rfft_fast_init_f32(&fft_instance, length);
        fft_initialized = true;
    }

    // 执行实数FFT
    arm_rfft_fast_f32(&fft_instance, input, fft_buffer, 0);

    // 计算幅度谱
    arm_cmplx_mag_f32(fft_buffer, output, length/2);

    // 归一化
    for (uint16_t i = 0; i < length/2; i++) {
        output[i] /= length;
    }
}

/**
 * @brief  计算频率响应
 * @param  input: 输入信号
 * @param  output: 输出信号
 * @param  frequency: 测试频率
 * @retval 频率响应结果
 */
FreqResponse_t DSP_CalculateFreqResponse(float* input, float* output, float frequency)
{
    FreqResponse_t result;

    result.frequency = frequency;

    // 计算输入输出信号的RMS值
    result.input_amplitude = DSP_CalculateRMS(input, FFT_SIZE);
    result.output_amplitude = DSP_CalculateRMS(output, FFT_SIZE);

    // 计算幅频响应 (dB)
    if (result.input_amplitude > 0.001f) {
        result.magnitude_db = 20.0f * log10f(result.output_amplitude / result.input_amplitude);
    } else {
        result.magnitude_db = -100.0f; // 极小值
    }

    // 计算相频响应
    result.phase_deg = DSP_CalculatePhase(input, output, FFT_SIZE);

    result.valid = 1;

    return result;
}

/**
 * @brief  PID控制器初始化
 * @param  pid: PID控制器结构体
 * @param  kp: 比例系数
 * @param  ki: 积分系数
 * @param  kd: 微分系数
 * @retval None
 */
void PID_Init(PID_Controller_t* pid, float kp, float ki, float kd)
{
    pid->kp = kp;
    pid->ki = ki;
    pid->kd = kd;
    pid->setpoint = 0.0f;
    pid->error = 0.0f;
    pid->integral = 0.0f;
    pid->derivative = 0.0f;
    pid->last_error = 0.0f;
    pid->output = 0.0f;

    USART_Printf("PID Init: Kp=%.2f, Ki=%.2f, Kd=%.2f\r\n", kp, ki, kd);
}

/**
 * @brief  PID控制器更新
 * @param  pid: PID控制器结构体
 * @param  setpoint: 目标值
 * @param  measured_value: 测量值
 * @retval 控制输出
 */
float PID_Update(PID_Controller_t* pid, float setpoint, float measured_value)
{
    pid->setpoint = setpoint;
    pid->error = setpoint - measured_value;

    // 积分项
    pid->integral += pid->error;

    // 积分限幅
    if (pid->integral > 100.0f) pid->integral = 100.0f;
    if (pid->integral < -100.0f) pid->integral = -100.0f;

    // 微分项
    pid->derivative = pid->error - pid->last_error;

    // PID输出
    pid->output = pid->kp * pid->error +
                  pid->ki * pid->integral +
                  pid->kd * pid->derivative;

    // 输出限幅
    if (pid->output > 5.0f) pid->output = 5.0f;
    if (pid->output < 0.0f) pid->output = 0.0f;

    pid->last_error = pid->error;

    return pid->output;
}

/**
 * @brief  PID控制器复位
 * @param  pid: PID控制器结构体
 * @retval None
 */
void PID_Reset(PID_Controller_t* pid)
{
    pid->error = 0.0f;
    pid->integral = 0.0f;
    pid->derivative = 0.0f;
    pid->last_error = 0.0f;
    pid->output = 0.0f;
}

/**
 * @brief  频率扫描模块初始化
 * @param  None
 * @retval None
 */
void FreqSweep_Init(void)
{
    // 清零频率响应数组
    for (uint8_t i = 0; i < FREQ_SWEEP_POINTS; i++) {
        g_freq_response[i].frequency = 0.0f;
        g_freq_response[i].magnitude_db = 0.0f;
        g_freq_response[i].phase_deg = 0.0f;
        g_freq_response[i].valid = 0;
    }

    USART_Printf("Frequency Sweep Init Complete\r\n");
}

/**
 * @brief  单点频率测试
 * @param  frequency: 测试频率
 * @param  result: 测试结果
 * @retval 0: 成功, 1: 失败
 */
uint8_t FreqSweep_SinglePoint(uint32_t frequency, FreqResponse_t* result)
{
    USART_Printf("Testing Frequency: %luHz\r\n", frequency);

    // 1. 设置DDS输出频率
    G_DDS_SetFrequency(frequency);

    // 2. 等待系统稳定
    for (volatile uint32_t i = 0; i < 100000; i++);

    // 3. 采集数据
    if (DataAcquisition_GetData(g_input_signal, g_output_signal, 256) != 0) {
        return 1; // 采集失败
    }

    // 4. 计算频率响应
    *result = DSP_CalculateFreqResponse(g_input_signal, g_output_signal, (float)frequency);

    USART_Printf("Result: Mag=%.2fdB, Phase=%.1fdeg\r\n",
                 result->magnitude_db, result->phase_deg);

    return 0;
}

/**
 * @brief  自动频率扫描
 * @param  start_freq: 起始频率
 * @param  end_freq: 结束频率
 * @param  points: 扫描点数
 * @retval None
 */
void FreqSweep_Auto(uint32_t start_freq, uint32_t end_freq, uint8_t points)
{
    USART_Printf("Auto Sweep: %luHz - %luHz, %d points\r\n",
                 start_freq, end_freq, points);

    if (points > FREQ_SWEEP_POINTS) points = FREQ_SWEEP_POINTS;

    for (uint8_t i = 0; i < points; i++) {
        // 计算当前频率 (对数分布)
        float log_start = log10f((float)start_freq);
        float log_end = log10f((float)end_freq);
        float log_freq = log_start + (log_end - log_start) * i / (points - 1);
        uint32_t current_freq = (uint32_t)powf(10.0f, log_freq);

        // 执行单点测试
        if (FreqSweep_SinglePoint(current_freq, &g_freq_response[i]) == 0) {
            USART_Printf("Point %d: %luHz, %.2fdB, %.1fdeg\r\n",
                         i+1, current_freq,
                         g_freq_response[i].magnitude_db,
                         g_freq_response[i].phase_deg);
        }

        // 进度指示
        if ((i % 5) == 0) {
            USART_Printf("Progress: %d%%\r\n", (i * 100) / points);
        }
    }

    USART_Printf("Frequency Sweep Complete!\r\n");
}

/**
 * @brief  显示频率扫描结果
 * @param  None
 * @retval None
 */
void FreqSweep_DisplayResults(void)
{
    USART_Printf("\r\n=== Frequency Response Results ===\r\n");
    USART_Printf("Freq(Hz)\tMag(dB)\tPhase(deg)\r\n");
    USART_Printf("--------------------------------\r\n");

    for (uint8_t i = 0; i < FREQ_SWEEP_POINTS; i++) {
        if (g_freq_response[i].valid) {
            USART_Printf("%lu\t%.2f\t%.1f\r\n",
                         (uint32_t)g_freq_response[i].frequency,
                         g_freq_response[i].magnitude_db,
                         g_freq_response[i].phase_deg);
        }
    }

    USART_Printf("================================\r\n\r\n");
}

/**
 * @brief  系统状态机
 * @param  None
 * @retval None
 */
void System_StateMachine(void)
{
    static uint8_t system_state = 0;
    static uint32_t state_counter = 0;

    state_counter++;

    switch (system_state) {
        case 0: // 空闲状态
            if (state_counter > 1000000) {
                state_counter = 0;
                // 可以在这里添加自动测试逻辑
            }
            break;

        case 1: // 测试状态
            // 执行测试逻辑
            break;

        case 2: // 校准状态
            // 执行校准逻辑
            break;

        default:
            system_state = 0;
            break;
    }
}

/**
 * @brief  更新系统显示
 * @param  None
 * @retval None
 */
void System_UpdateDisplay(void)
{
    // 更新OLED显示当前系统状态
    static uint32_t display_counter = 0;

    display_counter++;
    if (display_counter > 500000) {
        display_counter = 0;

        // 显示当前DDS频率和幅度
        OLED_Clear();
        OLED_ShowString(0, 0, "Circuit Explorer", OLED_FONT_6x8);

        char freq_str[16];
        sprintf(freq_str, "F:%luHz", g_dds_config.frequency);
        OLED_ShowString(0, 16, freq_str, OLED_FONT_6x8);

        char amp_str[16];
        sprintf(amp_str, "A:%.1fV G:%d", g_dds_config.amplitude, g_agc_config.current_gain);
        OLED_ShowString(0, 32, amp_str, OLED_FONT_6x8);

        OLED_Refresh();
    }
}

/**
 * @brief  处理系统命令
 * @param  None
 * @retval None
 */
void System_ProcessCommands(void)
{
    // 这里可以添加串口命令处理逻辑
    // 例如：设置频率、幅度、启动扫描等
}
